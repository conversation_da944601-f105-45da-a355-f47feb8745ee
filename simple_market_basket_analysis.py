#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版购物篮分析 - 使用纯Python实现
不依赖mlxtend和networkx库
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from collections import defaultdict, Counter
from itertools import combinations
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class SimpleApriori:
    """简化的Apriori算法实现"""
    
    def __init__(self, transactions):
        self.transactions = transactions
        self.frequent_itemsets = {}
        self.rules = []
    
    def get_support(self, itemset):
        """计算项目集的支持度"""
        count = 0
        for transaction in self.transactions:
            if set(itemset).issubset(set(transaction)):
                count += 1
        return count / len(self.transactions)
    
    def get_frequent_1_itemsets(self, min_support):
        """获取频繁1-项集"""
        item_counts = Counter()
        for transaction in self.transactions:
            for item in transaction:
                item_counts[item] += 1
        
        frequent_1_itemsets = []
        for item, count in item_counts.items():
            support = count / len(self.transactions)
            if support >= min_support:
                frequent_1_itemsets.append(([item], support))
        
        return frequent_1_itemsets
    
    def generate_candidates(self, frequent_itemsets, k):
        """生成候选k-项集"""
        candidates = []
        n = len(frequent_itemsets)
        
        for i in range(n):
            for j in range(i + 1, n):
                itemset1 = frequent_itemsets[i][0]
                itemset2 = frequent_itemsets[j][0]
                
                # 合并两个(k-1)-项集
                union = sorted(list(set(itemset1) | set(itemset2)))
                if len(union) == k:
                    candidates.append(union)
        
        return candidates
    
    def run_apriori(self, min_support=0.01):
        """运行Apriori算法"""
        # 获取频繁1-项集
        frequent_itemsets = self.get_frequent_1_itemsets(min_support)
        self.frequent_itemsets[1] = frequent_itemsets
        
        k = 2
        while self.frequent_itemsets[k-1]:
            # 生成候选k-项集
            candidates = self.generate_candidates(self.frequent_itemsets[k-1], k)
            
            # 计算候选项集的支持度
            frequent_k_itemsets = []
            for candidate in candidates:
                support = self.get_support(candidate)
                if support >= min_support:
                    frequent_k_itemsets.append((candidate, support))
            
            self.frequent_itemsets[k] = frequent_k_itemsets
            k += 1
        
        return self.frequent_itemsets
    
    def generate_rules(self, min_confidence=0.5, min_lift=1.0):
        """生成关联规则"""
        self.rules = []
        
        for k in range(2, len(self.frequent_itemsets) + 1):
            for itemset, support in self.frequent_itemsets[k]:
                if len(itemset) < 2:
                    continue
                
                # 生成所有可能的规则
                for i in range(1, len(itemset)):
                    for antecedent in combinations(itemset, i):
                        consequent = [item for item in itemset if item not in antecedent]
                        
                        # 计算置信度
                        antecedent_support = self.get_support(antecedent)
                        if antecedent_support > 0:
                            confidence = support / antecedent_support
                            
                            # 计算提升度
                            consequent_support = self.get_support(consequent)
                            if consequent_support > 0:
                                lift = confidence / consequent_support
                                
                                if confidence >= min_confidence and lift >= min_lift:
                                    self.rules.append({
                                        'antecedents': list(antecedent),
                                        'consequents': consequent,
                                        'support': support,
                                        'confidence': confidence,
                                        'lift': lift
                                    })
        
        return self.rules

class SimpleMarketBasketAnalyzer:
    """简化的购物篮分析器"""
    
    def __init__(self):
        self.transactions = []
        self.apriori = None
        self.frequent_itemsets = {}
        self.rules = []
    
    def generate_sample_data(self, n_transactions=7500):
        """生成示例数据"""
        items = [
            'mineral water', 'eggs', 'spaghetti', 'french fries', 'chocolate',
            'green tea', 'milk', 'ground beef', 'frozen vegetables', 'pancakes',
            'pasta', 'escalope', 'shrimp', 'olive oil', 'herb & pepper',
            'tomato sauce', 'mushroom cream sauce', 'whole wheat pasta',
            'bread', 'butter', 'cheese', 'yogurt', 'chicken', 'salmon'
        ]
        
        # 设置随机种子以确保结果可重现
        np.random.seed(42)
        
        transactions = []
        for _ in range(n_transactions):
            # 每个交易包含1-6个商品
            n_items = np.random.randint(1, 7)
            transaction = np.random.choice(items, size=n_items, replace=False).tolist()
            transactions.append(transaction)
        
        self.transactions = transactions
        return transactions
    
    def run_analysis(self, min_support=0.01, min_confidence=0.3, min_lift=0.5):
        """运行完整分析"""
        print(f"开始分析 {len(self.transactions)} 笔交易...")

        # 创建Apriori实例
        self.apriori = SimpleApriori(self.transactions)

        # 运行Apriori算法
        print("正在挖掘频繁项集...")
        self.frequent_itemsets = self.apriori.run_apriori(min_support)

        # 生成关联规则
        print("正在生成关联规则...")
        self.rules = self.apriori.generate_rules(min_confidence, min_lift)

        # 统计结果
        total_itemsets = sum(len(itemsets) for itemsets in self.frequent_itemsets.values())
        print(f"发现 {total_itemsets} 个频繁项集")
        print(f"生成 {len(self.rules)} 条关联规则")

        return self.frequent_itemsets, self.rules
    
    def print_results(self):
        """打印分析结果"""
        print("\n=== 频繁项集统计 ===")
        for k, itemsets in self.frequent_itemsets.items():
            if itemsets:
                print(f"{k}-项集: {len(itemsets)} 个")
        
        print(f"\n=== Top 10 频繁1-项集 ===")
        if 1 in self.frequent_itemsets:
            sorted_1_itemsets = sorted(self.frequent_itemsets[1], key=lambda x: x[1], reverse=True)
            for i, (itemset, support) in enumerate(sorted_1_itemsets[:10]):
                print(f"{i+1}. {itemset[0]}: {support:.3f}")
        
        print(f"\n=== Top 10 关联规则 ===")
        sorted_rules = sorted(self.rules, key=lambda x: x['confidence'], reverse=True)
        for i, rule in enumerate(sorted_rules[:10]):
            ant = ', '.join(rule['antecedents'])
            cons = ', '.join(rule['consequents'])
            print(f"{i+1}. {ant} → {cons}")
            print(f"   支持度: {rule['support']:.3f}, 置信度: {rule['confidence']:.3f}, 提升度: {rule['lift']:.3f}")
    
    def plot_item_frequency(self):
        """绘制商品频率图"""
        # 统计商品频率
        item_counts = Counter()
        for transaction in self.transactions:
            for item in transaction:
                item_counts[item] += 1
        
        # 获取前20个商品
        top_items = item_counts.most_common(20)
        items, counts = zip(*top_items)
        
        plt.figure(figsize=(12, 8))
        bars = plt.bar(range(len(items)), counts, color='skyblue', alpha=0.8)
        plt.xlabel('商品', fontsize=12)
        plt.ylabel('购买次数', fontsize=12)
        plt.title('Top 20 商品购买频率分析', fontsize=14, fontweight='bold')
        plt.xticks(range(len(items)), items, rotation=45, ha='right')
        
        # 添加数值标签
        for i, bar in enumerate(bars):
            height = bar.get_height()
            plt.text(bar.get_x() + bar.get_width()/2., height + 10,
                    f'{int(height)}', ha='center', va='bottom', fontsize=10)
        
        plt.tight_layout()
        plt.grid(axis='y', alpha=0.3)
        plt.savefig('商品购买频率分析.png', dpi=300, bbox_inches='tight')
        plt.show()
    
    def plot_frequent_itemsets_distribution(self):
        """绘制频繁项集分布图"""
        lengths = []
        counts = []
        
        for k, itemsets in self.frequent_itemsets.items():
            if itemsets:
                lengths.append(k)
                counts.append(len(itemsets))
        
        plt.figure(figsize=(10, 6))
        bars = plt.bar(lengths, counts, color=['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4'], alpha=0.8)
        plt.xlabel('项集长度', fontsize=12)
        plt.ylabel('频繁项集数量', fontsize=12)
        plt.title('频繁项集长度分布', fontsize=14, fontweight='bold')
        
        # 添加数值标签
        for bar in bars:
            height = bar.get_height()
            plt.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                    f'{int(height)}', ha='center', va='bottom', fontsize=11)
        
        plt.grid(axis='y', alpha=0.3)
        plt.tight_layout()
        plt.savefig('频繁项集分布.png', dpi=300, bbox_inches='tight')
        plt.show()
    
    def plot_rules_scatter(self):
        """绘制关联规则散点图"""
        if not self.rules:
            print("没有关联规则可以绘制")
            return
        
        supports = [rule['support'] for rule in self.rules]
        confidences = [rule['confidence'] for rule in self.rules]
        lifts = [rule['lift'] for rule in self.rules]
        
        plt.figure(figsize=(10, 8))
        scatter = plt.scatter(supports, confidences, c=lifts, cmap='viridis', 
                            s=60, alpha=0.7, edgecolors='black', linewidth=0.5)
        
        plt.xlabel('支持度 (Support)', fontsize=12)
        plt.ylabel('置信度 (Confidence)', fontsize=12)
        plt.title('关联规则支持度-置信度分布', fontsize=14, fontweight='bold')
        
        # 添加颜色条
        cbar = plt.colorbar(scatter)
        cbar.set_label('提升度 (Lift)', fontsize=12)
        
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        plt.savefig('关联规则散点图.png', dpi=300, bbox_inches='tight')
        plt.show()
    
    def generate_visualizations(self):
        """生成所有可视化图表"""
        print("正在生成可视化图表...")
        
        self.plot_item_frequency()
        self.plot_frequent_itemsets_distribution()
        self.plot_rules_scatter()
        
        print("图表生成完成！")

def main():
    """主函数"""
    print("=== 简化版购物篮分析 ===\n")
    
    # 创建分析器
    analyzer = SimpleMarketBasketAnalyzer()
    
    # 生成示例数据
    print("1. 生成示例数据...")
    analyzer.generate_sample_data(7500)
    
    # 运行分析
    print("\n2. 运行关联规则挖掘...")
    analyzer.run_analysis(min_support=0.01, min_confidence=0.3, min_lift=0.5)
    
    # 打印结果
    print("\n3. 分析结果:")
    analyzer.print_results()
    
    # 生成可视化
    print("\n4. 生成可视化图表...")
    analyzer.generate_visualizations()
    
    print("\n=== 分析完成！ ===")

if __name__ == "__main__":
    main()
