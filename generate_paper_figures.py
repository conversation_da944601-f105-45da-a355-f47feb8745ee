#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成论文中需要的所有图表
为论文《关联规则挖掘在超市购物篮分析中的应用》生成可视化图表
"""

import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
import seaborn as sns
from matplotlib.patches import Rectangle, FancyBboxPatch
import matplotlib.patches as mpatches

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

def create_concept_diagram():
    """图片1: 关联规则挖掘在零售业应用的概念图"""
    fig, ax = plt.subplots(figsize=(12, 8))
    
    # 绘制概念图
    # 数据源
    data_box = FancyBboxPatch((1, 6), 2, 1, boxstyle="round,pad=0.1", 
                             facecolor='lightblue', edgecolor='black')
    ax.add_patch(data_box)
    ax.text(2, 6.5, '交易数据', ha='center', va='center', fontsize=12, fontweight='bold')
    
    # 数据挖掘
    mining_box = FancyBboxPatch((5, 6), 2, 1, boxstyle="round,pad=0.1", 
                               facecolor='lightgreen', edgecolor='black')
    ax.add_patch(mining_box)
    ax.text(6, 6.5, '关联规则挖掘', ha='center', va='center', fontsize=12, fontweight='bold')
    
    # 应用场景
    apps = ['商品推荐', '货架布局', '促销策略', '库存管理']
    colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4']
    
    for i, (app, color) in enumerate(zip(apps, colors)):
        app_box = FancyBboxPatch((9, 4.5 + i * 1.2), 2, 0.8, boxstyle="round,pad=0.1", 
                                facecolor=color, edgecolor='black')
        ax.add_patch(app_box)
        ax.text(10, 4.9 + i * 1.2, app, ha='center', va='center', fontsize=11, fontweight='bold')
    
    # 绘制箭头
    ax.arrow(3.2, 6.5, 1.6, 0, head_width=0.2, head_length=0.2, fc='black', ec='black')
    ax.arrow(7.2, 6.5, 1.6, 0, head_width=0.2, head_length=0.2, fc='black', ec='black')
    
    # 从挖掘到应用的箭头
    for i in range(4):
        ax.arrow(8.8, 6.3, 0.8, -1.2 + i * 1.2, head_width=0.1, head_length=0.1, fc='gray', ec='gray')
    
    ax.set_xlim(0, 12)
    ax.set_ylim(3, 9)
    ax.set_title('关联规则挖掘在零售业的应用概念图', fontsize=16, fontweight='bold', pad=20)
    ax.axis('off')
    
    plt.tight_layout()
    plt.savefig('图1_关联规则挖掘应用概念图.png', dpi=300, bbox_inches='tight')
    plt.show()

def create_basic_concepts_diagram():
    """图片2: 关联规则挖掘基本概念示意图"""
    fig, ax = plt.subplots(figsize=(12, 8))
    
    # 示例交易数据
    transactions = [
        ['面包', '牛奶', '鸡蛋'],
        ['面包', '黄油'],
        ['牛奶', '鸡蛋', '奶酪'],
        ['面包', '牛奶', '黄油'],
        ['鸡蛋', '奶酪']
    ]
    
    # 绘制交易表
    y_start = 7
    ax.text(1, y_start + 0.5, '交易数据库', fontsize=14, fontweight='bold')
    
    for i, transaction in enumerate(transactions):
        ax.text(0.5, y_start - i * 0.5, f'T{i+1}:', fontsize=12, fontweight='bold')
        ax.text(1.5, y_start - i * 0.5, ', '.join(transaction), fontsize=12)
    
    # 绘制频繁项集
    ax.text(6, y_start + 0.5, '频繁项集', fontsize=14, fontweight='bold')
    frequent_items = [
        '1-项集: {面包}, {牛奶}, {鸡蛋}',
        '2-项集: {面包, 牛奶}, {牛奶, 鸡蛋}',
        '3-项集: {面包, 牛奶, 鸡蛋}'
    ]
    
    for i, item in enumerate(frequent_items):
        ax.text(6, y_start - i * 0.5, item, fontsize=11)
    
    # 绘制关联规则
    ax.text(1, 3.5, '关联规则示例', fontsize=14, fontweight='bold')
    rules = [
        '{面包} → {牛奶}',
        '{牛奶} → {鸡蛋}',
        '{面包, 牛奶} → {鸡蛋}'
    ]
    
    for i, rule in enumerate(rules):
        ax.text(1, 3 - i * 0.4, rule, fontsize=12)
        ax.text(6, 3 - i * 0.4, f'支持度: 0.{4-i}0, 置信度: 0.{6+i}0', fontsize=11)
    
    ax.set_xlim(0, 12)
    ax.set_ylim(0, 8.5)
    ax.set_title('关联规则挖掘基本概念示意图', fontsize=16, fontweight='bold', pad=20)
    ax.axis('off')
    
    plt.tight_layout()
    plt.savefig('图2_基本概念示意图.png', dpi=300, bbox_inches='tight')
    plt.show()

def create_apriori_flowchart():
    """图片3: Apriori算法流程图"""
    fig, ax = plt.subplots(figsize=(10, 12))
    
    # 流程步骤
    steps = [
        '开始',
        '扫描数据库\n计算1-项集支持度',
        '生成频繁1-项集 L₁',
        'k = 2',
        '由 Lₖ₋₁ 生成候选项集 Cₖ',
        '扫描数据库\n计算 Cₖ 支持度',
        '生成频繁k-项集 Lₖ',
        'Lₖ 为空?',
        '生成关联规则',
        '结束'
    ]
    
    # 位置和颜色
    positions = [(5, 11), (5, 9.5), (5, 8), (5, 6.5), (5, 5), (5, 3.5), (5, 2), (5, 0.5), (8, 2), (8, 0.5)]
    colors = ['lightgreen', 'lightblue', 'lightblue', 'lightyellow', 'lightblue', 'lightblue', 'lightblue', 'orange', 'lightcoral', 'lightgreen']
    
    # 绘制流程框
    for i, (step, pos, color) in enumerate(zip(steps, positions, colors)):
        if i in [0, 9]:  # 开始和结束用椭圆
            box = plt.Circle(pos, 0.5, facecolor=color, edgecolor='black')
        elif i == 7:  # 判断用菱形
            box = FancyBboxPatch((pos[0]-0.8, pos[1]-0.4), 1.6, 0.8, boxstyle="round,pad=0.1", 
                               facecolor=color, edgecolor='black')
        else:  # 其他用矩形
            box = FancyBboxPatch((pos[0]-1, pos[1]-0.4), 2, 0.8, boxstyle="round,pad=0.1", 
                               facecolor=color, edgecolor='black')
        ax.add_patch(box)
        ax.text(pos[0], pos[1], step, ha='center', va='center', fontsize=10, fontweight='bold')
    
    # 绘制箭头
    arrows = [(0, 1), (1, 2), (2, 3), (3, 4), (4, 5), (5, 6), (6, 7), (7, 8), (8, 9)]
    for start, end in arrows:
        start_pos = positions[start]
        end_pos = positions[end]
        if start == 7 and end == 8:  # 是的分支
            ax.arrow(start_pos[0] + 0.8, start_pos[1], 2.2, 1.5, head_width=0.1, head_length=0.1, fc='black', ec='black')
            ax.text(6.5, 1.5, '是', fontsize=10, fontweight='bold')
        else:
            ax.arrow(start_pos[0], start_pos[1] - 0.4, 0, -0.7, head_width=0.1, head_length=0.1, fc='black', ec='black')
    
    # 循环箭头
    ax.arrow(4.2, 0.5, -2.5, 0, head_width=0.1, head_length=0.1, fc='red', ec='red')
    ax.arrow(1.7, 0.5, 0, 5.5, head_width=0.1, head_length=0.1, fc='red', ec='red')
    ax.arrow(1.7, 6, 2.8, 0, head_width=0.1, head_length=0.1, fc='red', ec='red')
    ax.text(3, 6.2, 'k = k + 1', fontsize=10, fontweight='bold', color='red')
    ax.text(1, 0.2, '否', fontsize=10, fontweight='bold', color='red')
    
    ax.set_xlim(0, 10)
    ax.set_ylim(-0.5, 12)
    ax.set_title('Apriori算法流程图', fontsize=16, fontweight='bold', pad=20)
    ax.axis('off')
    
    plt.tight_layout()
    plt.savefig('图3_Apriori算法流程图.png', dpi=300, bbox_inches='tight')
    plt.show()

def create_metrics_example():
    """图片4: 评价指标计算示例图"""
    fig, ax = plt.subplots(figsize=(12, 8))
    
    # 示例数据
    ax.text(6, 7.5, '评价指标计算示例', fontsize=16, fontweight='bold', ha='center')
    
    # 规则示例
    ax.text(1, 6.5, '规则: {面包} → {牛奶}', fontsize=14, fontweight='bold', 
           bbox=dict(boxstyle="round,pad=0.3", facecolor='lightblue'))
    
    # 计算过程
    calculations = [
        '总交易数: 1000',
        '包含{面包}的交易数: 300',
        '包含{牛奶}的交易数: 400', 
        '同时包含{面包, 牛奶}的交易数: 200',
        '',
        '支持度 = 200/1000 = 0.20 (20%)',
        '置信度 = 200/300 = 0.67 (67%)',
        '提升度 = 0.67/0.40 = 1.67'
    ]
    
    for i, calc in enumerate(calculations):
        if calc == '':
            continue
        color = 'lightgreen' if i >= 5 else 'white'
        ax.text(1, 5.5 - i * 0.4, calc, fontsize=12, 
               bbox=dict(boxstyle="round,pad=0.2", facecolor=color))
    
    # 解释
    explanations = [
        '支持度: 衡量规则的重要性',
        '置信度: 衡量规则的可靠性', 
        '提升度 > 1: 表示正相关'
    ]
    
    for i, exp in enumerate(explanations):
        ax.text(7, 3 - i * 0.5, exp, fontsize=12, 
               bbox=dict(boxstyle="round,pad=0.2", facecolor='lightyellow'))
    
    ax.set_xlim(0, 12)
    ax.set_ylim(0, 8)
    ax.axis('off')
    
    plt.tight_layout()
    plt.savefig('图4_评价指标计算示例.png', dpi=300, bbox_inches='tight')
    plt.show()

def create_data_preprocessing_flow():
    """图片5: 数据预处理流程图"""
    fig, ax = plt.subplots(figsize=(12, 8))
    
    # 流程步骤
    steps = [
        ('原始CSV数据', 'lightblue'),
        ('数据清洗', 'lightgreen'),
        ('格式标准化', 'lightyellow'),
        ('事务编码', 'lightcoral'),
        ('二进制矩阵', 'lightgray')
    ]
    
    # 示例数据
    examples = [
        'bread, milk, eggs\nbread, butter\n...',
        '去除空值\n统一格式\n异常检测',
        '小写转换\n去除空格\n商品映射',
        'bread → 1\nmilk → 2\neggs → 3',
        '[[1,1,1,0],\n [1,0,0,1],\n ...]'
    ]
    
    # 绘制流程
    for i, ((step, color), example) in enumerate(zip(steps, examples)):
        x = 2 + i * 2
        
        # 步骤框
        step_box = FancyBboxPatch((x-0.8, 6), 1.6, 0.8, boxstyle="round,pad=0.1", 
                                 facecolor=color, edgecolor='black')
        ax.add_patch(step_box)
        ax.text(x, 6.4, step, ha='center', va='center', fontsize=11, fontweight='bold')
        
        # 示例框
        example_box = FancyBboxPatch((x-0.8, 3.5), 1.6, 1.5, boxstyle="round,pad=0.1", 
                                   facecolor='white', edgecolor='gray')
        ax.add_patch(example_box)
        ax.text(x, 4.25, example, ha='center', va='center', fontsize=9)
        
        # 箭头
        if i < len(steps) - 1:
            ax.arrow(x + 0.8, 6.4, 1.2, 0, head_width=0.1, head_length=0.1, fc='black', ec='black')
    
    ax.set_xlim(0, 12)
    ax.set_ylim(2, 8)
    ax.set_title('数据预处理流程', fontsize=16, fontweight='bold', pad=20)
    ax.axis('off')
    
    plt.tight_layout()
    plt.savefig('图5_数据预处理流程.png', dpi=300, bbox_inches='tight')
    plt.show()

def generate_all_figures():
    """生成所有论文图表"""
    print("正在生成论文图表...")
    
    print("1. 生成概念图...")
    create_concept_diagram()
    
    print("2. 生成基本概念示意图...")
    create_basic_concepts_diagram()
    
    print("3. 生成算法流程图...")
    create_apriori_flowchart()
    
    print("4. 生成评价指标示例...")
    create_metrics_example()
    
    print("5. 生成数据预处理流程...")
    create_data_preprocessing_flow()
    
    print("所有图表生成完成！")

if __name__ == "__main__":
    generate_all_figures()
