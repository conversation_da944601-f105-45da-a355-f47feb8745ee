#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
关联规则挖掘在超市购物篮分析中的应用
Market Basket Analysis using Association Rule Mining

作者：[您的姓名]
日期：2024年12月
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import networkx as nx
from mlxtend.frequent_patterns import apriori, association_rules
from mlxtend.preprocessing import TransactionEncoder
from collections import Counter
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class MarketBasketAnalyzer:
    """购物篮分析器"""
    
    def __init__(self):
        self.transactions = []
        self.df_encoded = None
        self.frequent_itemsets = None
        self.rules = None
        
    def generate_sample_data(self, n_transactions=7500):
        """生成示例购物篮数据"""
        # 定义商品列表
        items = [
            'mineral water', 'eggs', 'spaghetti', 'french fries', 'chocolate',
            'green tea', 'milk', 'ground beef', 'frozen vegetables', 'pancakes',
            'pasta', 'escalope', 'shrimp', 'olive oil', 'herb & pepper',
            'tomato sauce', 'mushroom cream sauce', 'whole wheat pasta',
            'bread', 'butter', 'cheese', 'yogurt', 'chicken', 'salmon',
            'rice', 'onions', 'tomatoes', 'lettuce', 'carrots', 'potatoes'
        ]
        
        # 定义商品购买概率（基于论文中的数据）
        item_probs = {
            'mineral water': 0.237, 'eggs': 0.207, 'spaghetti': 0.199,
            'french fries': 0.189, 'chocolate': 0.163, 'green tea': 0.158,
            'milk': 0.156, 'ground beef': 0.154, 'frozen vegetables': 0.143,
            'pancakes': 0.142, 'pasta': 0.120, 'escalope': 0.079,
            'shrimp': 0.071, 'olive oil': 0.066, 'herb & pepper': 0.049,
            'tomato sauce': 0.185, 'mushroom cream sauce': 0.096,
            'whole wheat pasta': 0.029, 'bread': 0.134, 'butter': 0.089,
            'cheese': 0.098, 'yogurt': 0.087, 'chicken': 0.112,
            'salmon': 0.067, 'rice': 0.145, 'onions': 0.123,
            'tomatoes': 0.156, 'lettuce': 0.098, 'carrots': 0.089,
            'potatoes': 0.134
        }
        
        # 生成交易数据
        np.random.seed(42)
        transactions = []
        
        for _ in range(n_transactions):
            transaction = []
            # 每个交易包含1-8个商品
            n_items = np.random.choice(range(1, 9), p=[0.1, 0.2, 0.25, 0.2, 0.15, 0.05, 0.03, 0.02])
            
            # 根据概率选择商品
            available_items = list(item_probs.keys())
            probs = list(item_probs.values())
            
            selected_items = np.random.choice(
                available_items, 
                size=min(n_items, len(available_items)), 
                replace=False, 
                p=np.array(probs) / np.sum(probs)
            )
            
            transaction = list(selected_items)
            transactions.append(transaction)
        
        self.transactions = transactions
        return transactions
    
    def load_data_from_csv(self, file_path):
        """从CSV文件加载数据"""
        try:
            data = pd.read_csv(file_path, header=None)
            transactions = []
            
            for i in range(len(data)):
                transaction = [str(data.values[i, j]) for j in range(len(data.columns)) 
                              if str(data.values[i, j]) != 'nan' and str(data.values[i, j]) != '']
                if len(transaction) > 0:
                    transactions.append(transaction)
            
            self.transactions = transactions
            print(f"成功加载 {len(transactions)} 笔交易数据")
            return transactions
        except FileNotFoundError:
            print(f"文件 {file_path} 不存在，使用示例数据")
            return self.generate_sample_data()
    
    def preprocess_data(self):
        """数据预处理"""
        # 使用TransactionEncoder进行编码
        te = TransactionEncoder()
        te_ary = te.fit(self.transactions).transform(self.transactions)
        self.df_encoded = pd.DataFrame(te_ary, columns=te.columns_)
        
        print(f"数据预处理完成:")
        print(f"- 交易数量: {len(self.transactions)}")
        print(f"- 商品种类: {len(te.columns_)}")
        print(f"- 数据矩阵形状: {self.df_encoded.shape}")
        
        return self.df_encoded
    
    def run_apriori(self, min_support=0.01, min_confidence=0.5, min_lift=1.0):
        """运行Apriori算法"""
        if self.df_encoded is None:
            self.preprocess_data()
        
        # 挖掘频繁项集
        print("正在挖掘频繁项集...")
        self.frequent_itemsets = apriori(
            self.df_encoded, 
            min_support=min_support, 
            use_colnames=True
        )
        
        # 生成关联规则
        print("正在生成关联规则...")
        if len(self.frequent_itemsets) > 0:
            self.rules = association_rules(
                self.frequent_itemsets,
                metric="confidence",
                min_threshold=min_confidence
            )
            
            # 过滤提升度
            self.rules = self.rules[self.rules['lift'] >= min_lift]
        else:
            self.rules = pd.DataFrame()
        
        print(f"挖掘完成:")
        print(f"- 频繁项集数量: {len(self.frequent_itemsets)}")
        print(f"- 关联规则数量: {len(self.rules)}")
        
        return self.frequent_itemsets, self.rules
    
    def analyze_results(self):
        """分析结果"""
        if self.frequent_itemsets is None or self.rules is None:
            print("请先运行Apriori算法")
            return
        
        print("\n=== 分析结果 ===")
        
        # 频繁项集统计
        itemset_lengths = self.frequent_itemsets['itemsets'].apply(len)
        print(f"\n频繁项集分布:")
        for length in sorted(itemset_lengths.unique()):
            count = (itemset_lengths == length).sum()
            print(f"- {length}-项集: {count}个")
        
        # Top频繁项集
        print(f"\nTop 10 频繁项集:")
        top_itemsets = self.frequent_itemsets.nlargest(10, 'support')
        for idx, row in top_itemsets.iterrows():
            items = ', '.join(list(row['itemsets']))
            print(f"- {items}: {row['support']:.3f}")
        
        # Top关联规则
        if len(self.rules) > 0:
            print(f"\nTop 10 关联规则:")
            top_rules = self.rules.nlargest(10, 'confidence')
            for idx, row in top_rules.iterrows():
                antecedents = ', '.join(list(row['antecedents']))
                consequents = ', '.join(list(row['consequents']))
                print(f"- {antecedents} → {consequents}: "
                      f"支持度={row['support']:.3f}, "
                      f"置信度={row['confidence']:.3f}, "
                      f"提升度={row['lift']:.3f}")
        
        return top_itemsets, self.rules

    def plot_item_frequency(self, top_n=20):
        """图片1: 商品购买频率柱状图"""
        if self.df_encoded is None:
            print("请先进行数据预处理")
            return

        # 计算商品频率
        item_freq = self.df_encoded.sum().sort_values(ascending=False).head(top_n)

        plt.figure(figsize=(12, 8))
        bars = plt.bar(range(len(item_freq)), item_freq.values, color='skyblue', alpha=0.8)
        plt.xlabel('商品', fontsize=12)
        plt.ylabel('购买次数', fontsize=12)
        plt.title(f'Top {top_n} 商品购买频率分析', fontsize=14, fontweight='bold')
        plt.xticks(range(len(item_freq)), item_freq.index, rotation=45, ha='right')

        # 添加数值标签
        for i, bar in enumerate(bars):
            height = bar.get_height()
            plt.text(bar.get_x() + bar.get_width()/2., height + 10,
                    f'{int(height)}', ha='center', va='bottom', fontsize=10)

        plt.tight_layout()
        plt.grid(axis='y', alpha=0.3)
        plt.savefig('图1_商品购买频率分析.png', dpi=300, bbox_inches='tight')
        plt.show()

    def plot_frequent_itemsets_distribution(self):
        """图片2: 频繁项集分布图"""
        if self.frequent_itemsets is None:
            print("请先运行Apriori算法")
            return

        # 统计不同长度的频繁项集数量
        itemset_lengths = self.frequent_itemsets['itemsets'].apply(len)
        length_counts = itemset_lengths.value_counts().sort_index()

        plt.figure(figsize=(10, 6))
        bars = plt.bar(length_counts.index, length_counts.values,
                      color=['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4'], alpha=0.8)
        plt.xlabel('项集长度', fontsize=12)
        plt.ylabel('频繁项集数量', fontsize=12)
        plt.title('频繁项集长度分布', fontsize=14, fontweight='bold')

        # 添加数值标签
        for bar in bars:
            height = bar.get_height()
            plt.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                    f'{int(height)}', ha='center', va='bottom', fontsize=11)

        plt.grid(axis='y', alpha=0.3)
        plt.tight_layout()
        plt.savefig('图2_频繁项集分布.png', dpi=300, bbox_inches='tight')
        plt.show()

    def plot_support_confidence_scatter(self):
        """图片3: 支持度-置信度散点图"""
        if self.rules is None or len(self.rules) == 0:
            print("没有关联规则可以绘制")
            return

        plt.figure(figsize=(10, 8))
        scatter = plt.scatter(self.rules['support'], self.rules['confidence'],
                            c=self.rules['lift'], cmap='viridis',
                            s=60, alpha=0.7, edgecolors='black', linewidth=0.5)

        plt.xlabel('支持度 (Support)', fontsize=12)
        plt.ylabel('置信度 (Confidence)', fontsize=12)
        plt.title('关联规则支持度-置信度分布', fontsize=14, fontweight='bold')

        # 添加颜色条
        cbar = plt.colorbar(scatter)
        cbar.set_label('提升度 (Lift)', fontsize=12)

        # 添加网格
        plt.grid(True, alpha=0.3)

        # 标注一些重要的规则
        top_rules = self.rules.nlargest(5, 'confidence')
        for idx, row in top_rules.iterrows():
            plt.annotate(f"Lift: {row['lift']:.2f}",
                        (row['support'], row['confidence']),
                        xytext=(5, 5), textcoords='offset points',
                        fontsize=8, alpha=0.8)

        plt.tight_layout()
        plt.savefig('图3_支持度置信度散点图.png', dpi=300, bbox_inches='tight')
        plt.show()

    def plot_lift_distribution(self):
        """图片4: 提升度分布直方图"""
        if self.rules is None or len(self.rules) == 0:
            print("没有关联规则可以绘制")
            return

        plt.figure(figsize=(10, 6))
        plt.hist(self.rules['lift'], bins=20, color='lightcoral', alpha=0.7, edgecolor='black')
        plt.xlabel('提升度 (Lift)', fontsize=12)
        plt.ylabel('规则数量', fontsize=12)
        plt.title('关联规则提升度分布', fontsize=14, fontweight='bold')

        # 添加统计信息
        mean_lift = self.rules['lift'].mean()
        median_lift = self.rules['lift'].median()
        plt.axvline(mean_lift, color='red', linestyle='--', linewidth=2, label=f'平均值: {mean_lift:.2f}')
        plt.axvline(median_lift, color='blue', linestyle='--', linewidth=2, label=f'中位数: {median_lift:.2f}')

        plt.legend()
        plt.grid(axis='y', alpha=0.3)
        plt.tight_layout()
        plt.savefig('图4_提升度分布.png', dpi=300, bbox_inches='tight')
        plt.show()

    def plot_association_network(self, top_n=15):
        """图片5: 关联规则网络图"""
        if self.rules is None or len(self.rules) == 0:
            print("没有关联规则可以绘制")
            return

        # 选择top规则
        top_rules = self.rules.nlargest(top_n, 'lift')

        # 创建网络图
        G = nx.DiGraph()

        # 添加节点和边
        for idx, row in top_rules.iterrows():
            antecedents = list(row['antecedents'])[0] if len(row['antecedents']) == 1 else str(row['antecedents'])
            consequents = list(row['consequents'])[0] if len(row['consequents']) == 1 else str(row['consequents'])

            G.add_edge(antecedents, consequents,
                      weight=row['lift'],
                      support=row['support'],
                      confidence=row['confidence'])

        plt.figure(figsize=(14, 10))
        pos = nx.spring_layout(G, k=3, iterations=50)

        # 绘制节点
        node_sizes = [G.degree(node) * 300 + 500 for node in G.nodes()]
        nx.draw_networkx_nodes(G, pos, node_size=node_sizes,
                              node_color='lightblue', alpha=0.8)

        # 绘制边
        edges = G.edges()
        weights = [G[u][v]['weight'] for u, v in edges]
        nx.draw_networkx_edges(G, pos, width=[w*0.5 for w in weights],
                              alpha=0.6, edge_color='gray', arrows=True,
                              arrowsize=20, arrowstyle='->')

        # 绘制标签
        nx.draw_networkx_labels(G, pos, font_size=8, font_weight='bold')

        plt.title(f'Top {top_n} 关联规则网络图', fontsize=14, fontweight='bold')
        plt.axis('off')
        plt.tight_layout()
        plt.savefig('图5_关联规则网络图.png', dpi=300, bbox_inches='tight')
        plt.show()

    def plot_category_heatmap(self):
        """图片6: 商品类别关联热力图"""
        if self.rules is None or len(self.rules) == 0:
            print("没有关联规则可以绘制")
            return

        # 定义商品类别
        categories = {
            '主食类': ['spaghetti', 'pasta', 'whole wheat pasta', 'bread', 'rice', 'pancakes'],
            '蛋白质类': ['eggs', 'ground beef', 'escalope', 'shrimp', 'chicken', 'salmon'],
            '乳制品类': ['milk', 'butter', 'cheese', 'yogurt'],
            '蔬菜类': ['frozen vegetables', 'onions', 'tomatoes', 'lettuce', 'carrots', 'potatoes'],
            '调料类': ['olive oil', 'herb & pepper', 'tomato sauce', 'mushroom cream sauce'],
            '饮料类': ['mineral water', 'green tea'],
            '零食类': ['chocolate', 'french fries']
        }

        # 创建类别映射
        item_to_category = {}
        for category, items in categories.items():
            for item in items:
                item_to_category[item] = category

        # 统计类别间关联
        category_matrix = pd.DataFrame(0, index=categories.keys(), columns=categories.keys())

        for idx, row in self.rules.iterrows():
            antecedents = list(row['antecedents'])
            consequents = list(row['consequents'])

            for ant in antecedents:
                for cons in consequents:
                    ant_cat = item_to_category.get(ant, '其他')
                    cons_cat = item_to_category.get(cons, '其他')
                    if ant_cat in category_matrix.index and cons_cat in category_matrix.columns:
                        category_matrix.loc[ant_cat, cons_cat] += row['lift']

        plt.figure(figsize=(10, 8))
        sns.heatmap(category_matrix, annot=True, cmap='YlOrRd', fmt='.2f',
                   square=True, linewidths=0.5)
        plt.title('商品类别关联强度热力图', fontsize=14, fontweight='bold')
        plt.xlabel('后件类别', fontsize=12)
        plt.ylabel('前件类别', fontsize=12)
        plt.tight_layout()
        plt.savefig('图6_商品类别关联热力图.png', dpi=300, bbox_inches='tight')
        plt.show()

    def plot_parameter_sensitivity(self):
        """图片7: 参数敏感性分析"""
        if self.df_encoded is None:
            print("请先进行数据预处理")
            return

        # 不同支持度阈值下的结果
        support_thresholds = [0.005, 0.01, 0.02, 0.03, 0.05]
        itemset_counts = []
        rule_counts = []

        for min_sup in support_thresholds:
            freq_items = apriori(self.df_encoded, min_support=min_sup, use_colnames=True)
            itemset_counts.append(len(freq_items))

            if len(freq_items) > 0:
                rules = association_rules(freq_items, metric="confidence", min_threshold=0.5)
                rule_counts.append(len(rules))
            else:
                rule_counts.append(0)

        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

        # 频繁项集数量变化
        ax1.plot(support_thresholds, itemset_counts, 'bo-', linewidth=2, markersize=8)
        ax1.set_xlabel('最小支持度阈值', fontsize=12)
        ax1.set_ylabel('频繁项集数量', fontsize=12)
        ax1.set_title('支持度阈值对频繁项集数量的影响', fontsize=12, fontweight='bold')
        ax1.grid(True, alpha=0.3)

        # 关联规则数量变化
        ax2.plot(support_thresholds, rule_counts, 'ro-', linewidth=2, markersize=8)
        ax2.set_xlabel('最小支持度阈值', fontsize=12)
        ax2.set_ylabel('关联规则数量', fontsize=12)
        ax2.set_title('支持度阈值对关联规则数量的影响', fontsize=12, fontweight='bold')
        ax2.grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig('图7_参数敏感性分析.png', dpi=300, bbox_inches='tight')
        plt.show()

    def generate_all_visualizations(self):
        """生成所有可视化图表"""
        print("正在生成所有可视化图表...")

        # 图1: 商品购买频率分析
        self.plot_item_frequency()

        # 图2: 频繁项集分布
        self.plot_frequent_itemsets_distribution()

        # 图3: 支持度-置信度散点图
        self.plot_support_confidence_scatter()

        # 图4: 提升度分布
        self.plot_lift_distribution()

        # 图5: 关联规则网络图
        self.plot_association_network()

        # 图6: 商品类别关联热力图
        self.plot_category_heatmap()

        # 图7: 参数敏感性分析
        self.plot_parameter_sensitivity()

        print("所有图表生成完成！")


def main():
    """主函数"""
    print("=== 关联规则挖掘在超市购物篮分析中的应用 ===\n")

    # 创建分析器实例
    analyzer = MarketBasketAnalyzer()

    # 1. 加载数据（优先尝试从CSV文件加载，否则使用示例数据）
    print("1. 数据加载...")
    try:
        transactions = analyzer.load_data_from_csv('Market_Basket_Optimisation.csv')
    except:
        print("未找到数据文件，使用生成的示例数据")
        transactions = analyzer.generate_sample_data()

    # 2. 数据预处理
    print("\n2. 数据预处理...")
    analyzer.preprocess_data()

    # 3. 运行Apriori算法
    print("\n3. 运行Apriori算法...")
    frequent_itemsets, rules = analyzer.run_apriori(
        min_support=0.01,
        min_confidence=0.5,
        min_lift=1.0
    )

    # 4. 分析结果
    print("\n4. 分析结果...")
    analyzer.analyze_results()

    # 5. 生成可视化图表
    print("\n5. 生成可视化图表...")
    analyzer.generate_all_visualizations()

    # 6. 保存结果
    print("\n6. 保存分析结果...")
    if len(frequent_itemsets) > 0:
        frequent_itemsets.to_csv('频繁项集结果.csv', index=False, encoding='utf-8-sig')
        print("频繁项集结果已保存到: 频繁项集结果.csv")

    if len(rules) > 0:
        # 转换集合为字符串以便保存
        rules_export = rules.copy()
        rules_export['antecedents'] = rules_export['antecedents'].apply(lambda x: ', '.join(list(x)))
        rules_export['consequents'] = rules_export['consequents'].apply(lambda x: ', '.join(list(x)))
        rules_export.to_csv('关联规则结果.csv', index=False, encoding='utf-8-sig')
        print("关联规则结果已保存到: 关联规则结果.csv")

    print("\n=== 分析完成！ ===")
    print("论文中需要的所有图表已生成，请查看当前目录下的PNG文件。")


if __name__ == "__main__":
    main()
