#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
运行购物篮分析的简化脚本
"""

import sys
import os

# 检查并安装必要的包
def check_and_install_packages():
    """检查并安装必要的Python包"""
    required_packages = [
        'pandas',
        'numpy', 
        'matplotlib',
        'seaborn',
        'mlxtend',
        'networkx'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✓ {package} 已安装")
        except ImportError:
            missing_packages.append(package)
            print(f"✗ {package} 未安装")
    
    if missing_packages:
        print(f"\n需要安装以下包: {', '.join(missing_packages)}")
        print("请运行以下命令安装:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    return True

def main():
    """主函数"""
    print("=== 购物篮分析环境检查 ===")
    
    # 检查包依赖
    if not check_and_install_packages():
        print("\n请先安装缺失的包，然后重新运行此脚本。")
        return
    
    print("\n=== 开始运行分析 ===")
    
    # 导入并运行主分析
    try:
        from market_basket_analysis import main as run_analysis
        run_analysis()
    except Exception as e:
        print(f"运行分析时出错: {e}")
        print("请检查market_basket_analysis.py文件是否存在且正确。")

if __name__ == "__main__":
    main()
