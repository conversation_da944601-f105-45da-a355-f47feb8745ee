# 关联规则挖掘在超市购物篮分析中的应用

## 项目概述

本项目实现了基于Apriori算法的购物篮分析，用于发现超市商品之间的关联规则。项目包含完整的论文和可运行的Python代码，能够生成论文中需要的所有可视化图表。

## 文件结构

```
数据仓库/
├── 论文模版.md                    # 完整的学术论文
├── market_basket_analysis.py      # 主要分析代码
├── run_analysis.py               # 运行脚本
├── README.md                     # 说明文档
├── 期末作业要求.md                # 作业要求
└── Market_Basket_Optimisation.csv # 数据文件（可选）
```

## 环境要求

### Python版本
- Python 3.7 或更高版本

### 必需的Python包
```bash
pip install pandas numpy matplotlib seaborn mlxtend networkx
```

## 使用方法

### 方法1：直接运行（推荐）
```bash
python run_analysis.py
```

### 方法2：运行主分析脚本
```bash
python market_basket_analysis.py
```

## 功能特性

### 1. 数据处理
- 自动生成示例购物篮数据
- 支持从CSV文件加载真实数据
- 数据清洗和预处理
- 事务编码转换

### 2. 关联规则挖掘
- Apriori算法实现
- 频繁项集挖掘
- 关联规则生成
- 支持度、置信度、提升度计算

### 3. 可视化分析
程序会自动生成以下图表：

1. **图1_商品购买频率分析.png** - 商品购买频率柱状图
2. **图2_频繁项集分布.png** - 频繁项集长度分布图
3. **图3_支持度置信度散点图.png** - 关联规则质量分析
4. **图4_提升度分布.png** - 提升度分布直方图
5. **图5_关联规则网络图.png** - 商品关联网络图
6. **图6_商品类别关联热力图.png** - 类别间关联强度
7. **图7_参数敏感性分析.png** - 参数影响分析

### 4. 结果导出
- 频繁项集结果.csv
- 关联规则结果.csv

## 参数配置

可以在代码中调整以下参数：

```python
# 在main()函数中修改
frequent_itemsets, rules = analyzer.run_apriori(
    min_support=0.01,      # 最小支持度（1%）
    min_confidence=0.5,    # 最小置信度（50%）
    min_lift=1.0          # 最小提升度
)
```

## 数据格式

### CSV数据格式
如果使用自己的数据，CSV文件格式应为：
```
bread,milk,eggs
bread,butter
milk,eggs,cheese
...
```
每行代表一笔交易，商品之间用逗号分隔。

### 示例数据
如果没有提供CSV文件，程序会自动生成包含以下商品的示例数据：
- 基础食材：鸡蛋、牛奶、面包等
- 主食类：意大利面、米饭等
- 蛋白质：牛肉、鸡肉、虾等
- 调料：橄榄油、番茄酱等
- 饮料：矿泉水、绿茶等

## 论文结构

完整论文包含以下章节：
1. 引言（研究背景、目的、意义）
2. 理论基础（关联规则挖掘、Apriori算法）
3. 数据预处理（数据集介绍、清洗、转换）
4. 方法实现（算法实现、参数设置）
5. 实验分析（结果展示、可视化分析）
6. 结论与展望

## 图片插入位置

论文中标注了16个图片插入位置：
- 图片标注1-4：理论部分的概念图和流程图
- 图片标注5-9：数据预处理和方法实现相关图表
- 图片标注10-16：实验结果和分析图表

运行代码后生成的PNG文件对应图片标注10-16。

## 故障排除

### 常见问题

1. **包导入错误**
   ```bash
   pip install --upgrade pandas numpy matplotlib seaborn mlxtend networkx
   ```

2. **中文字体显示问题**
   - Windows：代码已配置SimHei字体
   - Mac/Linux：可能需要安装中文字体

3. **内存不足**
   - 减少数据量或降低最小支持度阈值

4. **图片不显示**
   - 检查matplotlib后端设置
   - 确保有图形界面环境

### 性能优化

- 对于大数据集，建议提高最小支持度阈值
- 可以限制最大项集长度以提高性能
- 使用更高效的FP-Growth算法（需要额外实现）

## 学术价值

本项目具有以下学术和实践价值：
- 验证经典Apriori算法在实际场景中的有效性
- 为零售业提供数据驱动的商业决策支持
- 展示完整的数据挖掘项目流程
- 提供可重现的研究结果

## 扩展方向

1. 集成更多关联规则挖掘算法
2. 添加时间序列分析功能
3. 结合推荐系统算法
4. 支持更多数据格式
5. 开发Web界面

## 联系信息

如有问题或建议，请联系项目作者。

---

**注意**：本项目仅用于学术研究和教学目的。
